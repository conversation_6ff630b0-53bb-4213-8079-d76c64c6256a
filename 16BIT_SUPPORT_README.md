# 16-Bit Image Support

Photo Center now supports processing 16-bit images throughout the entire pipeline while preserving the original bit depth when possible.

## Overview

The system has been enhanced to:
- Load and preserve 16-bit images from RAW files and supported formats (TIFF, PNG)
- Process images in their native bit depth throughout the centering pipeline
- Convert to 8-bit only when necessary (for model inference or unsupported output formats)
- Save images in appropriate formats that support the original bit depth

## Configuration

New configuration options in `config.yaml`:

```yaml
image_processing:
  output_format: "tiff"  # tiff recommended for 16-bit support
  preserve_bit_depth: true  # Preserve 16-bit when possible
  force_8bit_for_jpeg: true  # Always convert to 8-bit for JPEG output
```

### Configuration Options

- **`preserve_bit_depth`**: When `true`, maintains 16-bit precision throughout processing
- **`force_8bit_for_jpeg`**: When `true`, converts 16-bit images to 8-bit for JPEG output (JPEG doesn't support 16-bit)
- **`output_format`**: Recommended to use "tiff" for full 16-bit support

## Supported Formats

### Input Formats (16-bit support)
- **RAW files**: CR2, NEF, ARW, DNG, etc. (processed as 16-bit)
- **TIFF**: Supports both 8-bit and 16-bit
- **PNG**: Supports both 8-bit and 16-bit
- **JPEG**: 8-bit only (automatically converted if needed)

### Output Formats (16-bit support)
- **TIFF**: Full 16-bit support ✅
- **PNG**: Full 16-bit support ✅
- **JPEG**: 8-bit only (auto-converted) ⚠️

## Processing Pipeline

### 1. Image Loading
- RAW files are processed with 16-bit output when `preserve_bit_depth` is enabled
- Standard formats (TIFF, PNG) are loaded with original bit depth preserved
- JPEG files remain 8-bit (native format limitation)

### 2. Human Detection
- Models require 8-bit input, so images are temporarily converted for inference
- Original 16-bit data is preserved and used for all subsequent processing
- Detection coordinates work correctly with both bit depths

### 3. Centering and Cropping
- All centering algorithms work with 16-bit images
- OpenCV operations handle 16-bit data correctly
- Center of mass calculations use normalized 8-bit data for accuracy but preserve original bit depth

### 4. Output Saving
- **TIFF/PNG**: Saves in original bit depth (16-bit preserved)
- **JPEG**: Automatically converts to 8-bit (format requirement)
- Bit depth information is logged during processing

## Usage Examples

### CLI Processing
```bash
# Process RAW file with 16-bit preservation
uv run photo-center -i image.cr2 -o centered.tiff

# Batch process with 16-bit support
uv run photo-center -i /path/to/raw/files --batch
```

### Programmatic Usage
```python
from src.photo_center.utils.config import Config
from src.photo_center.image_processing.raw_processor import RawProcessor

# Configure for 16-bit processing
config = Config()
raw_processor = RawProcessor(config)

# Load image (preserves 16-bit)
image = raw_processor.load_image("image.cr2")
bit_depth = raw_processor.get_bit_depth(image)
print(f"Loaded {bit_depth}-bit image")

# Process normally - bit depth is preserved throughout
# ... (human detection, centering, etc.)

# Save with appropriate format
raw_processor.save_image(processed_image, "output.tiff")
```

## Technical Details

### Bit Depth Detection
The system automatically detects image bit depth:
- `np.uint16` → 16-bit
- `np.uint8` → 8-bit

### Model Inference Preparation
A preprocessing layer converts 16-bit images to 8-bit only for model inference:
```python
inference_image = raw_processor.prepare_for_inference(original_16bit_image)
```

### Memory Considerations
- 16-bit images use twice the memory of 8-bit images
- Processing may be slightly slower due to increased data size
- Benefits include better color gradation and reduced banding

## Backward Compatibility

All existing functionality remains unchanged:
- 8-bit images continue to work as before
- Default configuration maintains existing behavior
- No breaking changes to API or workflow

## Recommendations

1. **Use TIFF output format** for full 16-bit support
2. **Enable `preserve_bit_depth`** for RAW file processing
3. **Monitor memory usage** when processing large 16-bit images
4. **Use JPEG only when file size is critical** (quality will be reduced to 8-bit)

## Logging

The system now logs bit depth information:
```
INFO: RAW image loaded: shape=(4000, 6000, 3), dtype=uint16
DEBUG: Loaded image with 16-bit depth: image.cr2
DEBUG: Converted 16-bit to 8-bit for model inference
DEBUG: Saving 16-bit TIFF
INFO: Image saved: output.tiff (dtype: uint16)
```
